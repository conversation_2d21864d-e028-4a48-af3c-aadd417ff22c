{%- comment -%}
IMPLEMENTATION NOTE: Impact header with all the menu is an extremely complex piece of code. Due to requirements from design
team, we had to cover over hundred of potential combinations. As a consequence, this part (and all the menu snippets) are
extremely sensitive code that should (if possible) not be touched unless you have spend enough time to study and understand
the underlying logic of the menu.
{%- endcomment -%}

{%- if section.settings.show_country_selector and localization.available_countries.size > 1 -%}
  {%- assign will_show_country_selector = true -%}
{%- endif -%}

{%- if section.settings.show_locale_selector and localization.available_languages.size > 1 -%}
  {%- assign will_show_locale_selector = true -%}
{%- endif -%}

<style>
  :root {
    --sticky-header-enabled: {%- if section.settings.enable_sticky -%}1{% else %}0{% endif %};
  }

  #shopify-section-{{ section.id }} {
    position: sticky;
    --header-grid-template: {% if section.settings.mobile_logo_position == 'left' %}"logo secondary-nav" / auto 1fr{% else %}"main-nav logo secondary-nav" / minmax(0, 1fr) auto minmax(0, 1fr){% endif %};
    --header-padding-block: {% if section.settings.reduce_padding %}var(--spacing-4){% else %}var(--spacing-4-5){% endif %};
    --header-background-opacity: {{ section.settings.background_opacity | divided_by: 100.0 }};
    --header-background-blur-radius: {{ section.settings.background_blur_radius }}px;
    --header-transparent-text-color: {{ section.settings.transparent_header_text_color.rgb }};

    {%- if section.settings.logo != blank -%}
      --header-logo-width: {{ section.settings.logo_mobile_max_width }}px;
      --header-logo-height: {{ section.settings.logo_mobile_max_width | divided_by: section.settings.logo.aspect_ratio | round }}px;
    {%- endif -%}


    top: 0;
    z-index: 99;
  }

  {%- if section.settings.enable_sticky -%}
    .shopify-section--announcement-bar ~ #shopify-section-{{ section.id }} {
      top: calc(var(--sticky-announcement-bar-enabled, 0) * var(--announcement-bar-height, 0px));
    }
  {%- endif -%}

  {%- if section.settings.opening_from == 'left' -%}
    @media screen and (max-width: 699px) {
      .navigation-drawer {
        --drawer-content-max-height: calc(100vh - (var(--spacing-2) * 2));
      }
    }
  {%- endif -%}

  @media screen and (min-width: 700px) {
    #shopify-section-{{ section.id }} {
      {%- if section.settings.logo != blank -%}
        --header-logo-width: {{ section.settings.logo_max_width }}px;
        --header-logo-height: {{ section.settings.logo_max_width | divided_by: section.settings.logo.aspect_ratio | round }}px;
      {%- endif -%}

      --header-padding-block: {% if section.settings.reduce_padding %}var(--spacing-6){% else %}var(--spacing-8-5){% endif %};
      --header-grid-template: "main-nav logo secondary-nav" / minmax(0, 1fr) auto minmax(0, 1fr);
    }
  }

  {%- if section.settings.layout contains 'logo_left' -%}
    @media screen and (min-width: 1150px) {
      {%- if section.settings.secondary_menu != blank or will_show_country_selector or will_show_locale_selector -%}
        {%- assign max_percent = '55%' -%}
      {%- else -%}
        {%- assign max_percent = '70%' -%}
      {%- endif -%}

      #shopify-section-{{ section.id }} {
        --header-grid-template: "logo main-nav secondary-nav" / {% if section.settings.layout == 'logo_left_navigation_inline' %}auto minmax(0, 1fr) minmax(0, max-content){% else %}minmax(0, 1fr) minmax(0, max-content) minmax(0, max-content){% endif %};
      }
    }
  {%- endif -%}


  .reversed-link, .link-faded-reverse{
    text-transform: uppercase !important;
  }

  .cart_text {
    align-items: center;
    margin-top: 1px;
    padding-top: 5px;
    display: flex;
    font-size: {{ section.settings.cart_text_font_size }}px;
    font-weight: {{ section.settings.cart_text_font_weight }};
  }



store-header.header {
    position: relative;
    display: block;
}

/* Mobile header styling */
.mobile-icons {
  display: flex;
  align-items: center;
}

.mobile-cart .cart-button {
  display: flex;
  align-items: center;
  padding: 8px 18px;
  background: {{ section.settings.mobile_cart_background }};
  color: #E7E8E5;
  border-radius: 10px;
  font-weight: normal;
  font-size: 18px;
}

@media screen and (max-width: 699px) {
  .mobile-cart .cart-button {
    font-size: {{ section.settings.mobile_cart_text_font_size }}px;
  }
}

@media screen and (min-width: 1150px) {
    .header__link-list {
        padding-top: 2px;
    }
}

.mobile-menu {
  margin-left: 10px;
  display: none;
}

@media screen and (max-width: 699px) {
  .mobile-menu {
    display: block;
  }
}

.mobile-menu-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  background: none;
  border: none;
  cursor: pointer;
}

.hamburger-icon {
  width: 24px;
  height: 20px;
  position: relative;
  margin-left: 5px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.hamburger-icon span {
  display: block;
  height: 3px;
  width: 100%;
  background-color: #2a2a2a;
  border-radius: 3px;
}

/* Desktop menu alignment - position menu items next to cart */
@media screen and (min-width: 1150px) {
  .desktop-menu-right {
    justify-content: flex-end;
  }

  /* Adjust spacing between menu items and cart */
  .header__main-nav {
    padding-right: 0;
  }


  /* Ensure menu items are aligned with cart button */
  .header__link-list {
    margin-right: var(--spacing-2);
  }
}

/* Mobile header layout updates */
@media screen and (max-width: 699px) {
  .header__wrapper {
    display: grid;
    grid-template-columns: {% if section.settings.mobile_logo_position == 'left' %}auto 1fr{% else %}minmax(0, 1fr) auto minmax(0, 1fr){% endif %};
    grid-template-areas: {% if section.settings.mobile_logo_position == 'left' %}"logo secondary-nav"{% else %}"main-nav logo secondary-nav"{% endif %};
    align-items: center;
  }

  .header__main-nav {
    {% if section.settings.mobile_logo_position == 'left' %}
    display: none;
    {% endif %}
  }

  .header__logo {
    {% if section.settings.mobile_logo_position == 'left' %}
    justify-self: start;
    {% else %}
    justify-self: center;
    {% endif %}
  }

  .header__secondary-nav {
    justify-self: end;
  }

  .mobile-menu-button {
    margin-left: 12px;
  }
}
</style>

<div id="shopify-section-{{ section.id }}">
<height-observer variable="header">
  <store-header class="header" {% if section.settings.hide_on_scroll %}hide-on-scroll="100"{% endif %} sticky="true" style="position: sticky; top: 0; z-index: 999;">
    <div class="header__wrapper">
      {%- comment -%}
      --------------------------------------------------------------------------------------------------------------------
      MAIN NAV AREA
      --------------------------------------------------------------------------------------------------------------------
      {%- endcomment -%}

      <div class="header__main-nav">
        <div class="header__icon-list">
          {% unless section.settings.mobile_logo_position == 'left' %}
          <button type="button" class="tap-area {% if section.settings.layout != 'drawer' %}lg:hidden{% endif %}" aria-controls="header-sidebar-menu" aria-expanded="false" data-action="open-drawer">
            <span class="sr-only">{{ 'header.general.menu' | t }}</span>
            {%- render 'icon' with 'hamburger' -%}
          </button>
          {% endunless %}

          {% comment %} <a href="{{ routes.search_url }}" class="tap-area sm:hidden" aria-controls="search-drawer">
            <span class="sr-only">{{ 'header.general.search' | t }}</span>
            {%- render 'icon' with 'search' -%}
          </a> {% endcomment %}

          {%- if section.settings.layout != 'drawer' and section.settings.menu.links.size > 0 -%}
            <nav class="header__link-list {% if section.settings.layout == 'logo_left_navigation_center' %}desktop-menu-right{% endif %} wrap" role="navigation">
              <ul class="contents" role="list">
                {%- for link in section.settings.menu.links -%}
                  {% liquid
                    assign link_title_downcase = link.title | strip | downcase
                    assign mega_menu_block = ''

                    for block in section.blocks
                      assign menu_item_downcase = block.settings.menu_item | strip | downcase

                      if menu_item_downcase == link_title_downcase
                        assign mega_menu_block = block
                        break
                      endif

                    endfor
                  %}

                  <li>
                    {%- if link.levels == 0 and mega_menu_block == '' -%}
                      <a href="{{ link.url }}" class="link-faded-reverse" {% if link.current %}aria-current="page"{% endif %}>{{- link.title -}}</a>
                    {%- else -%}
                      {%- if mega_menu_block != '' -%}
                        {%- comment -%}
                        Implementation note: the designers had the desire to show a lot of different kind of menus in Impact: one standard
                        mega menu but also a kind of "sidebar" mode when the mega-menu appears in a drawer. Unfortunately, this comes with
                        a lot of extra complexity as we have to go through two different code path: a "details" element for the standard
                        menu, and a full drawer for the other one.
                        {%- endcomment -%}
                        {%- unless mega_menu_block.settings.layout == 'drawer' -%}
                          <details is="mega-menu-disclosure" trigger="{{ section.settings.menu_open_trigger | escape }}" {{ mega_menu_block.shopify_attributes }}>
                            <summary class="text-with-icon gap-2.5 bold link-faded-reverse" {% if link.url != '#' %}data-url="{{ link.url | escape }}"{% endif %}>
                              {{- link.title -}} {%- render 'icon' with 'chevron-bottom' -%}
                            </summary>

                           {%- render 'mega-menu-horizontal', link: link, mega_menu_block: mega_menu_block -%}
                          </details>
                        {%- else -%}
                          <button aria-controls="navigation-drawer-{{ forloop.index }}" aria-expanded="false" class="text-with-icon gap-2.5 bold link-faded-reverse">
                            {{- link.title -}} {%- render 'icon' with 'chevron-bottom' -%}
                          </button>

                          <navigation-drawer class="navigation-drawer drawer lg-max:hidden" id="navigation-drawer-{{ forloop.index }}" open-from="left" mega-menu>
                            {%- render 'navigation-panel', navigation_layout: section.settings.layout, main_menu: link, is_mega_menu: true, mega_menu_block: mega_menu_block -%}
                          </navigation-drawer>
                        {%- endunless -%}
                      {%- else -%}
                        <details class="relative" is="dropdown-disclosure" trigger="{{ section.settings.menu_open_trigger | escape }}">
                          <summary class="text-with-icon gap-2.5 bold link-faded-reverse" {% if link.url != '#' %}data-url="{{ link.url | escape }}"{% endif %}>
                            {{- link.title -}} {%- render 'icon' with 'chevron-bottom' -%}
                          </summary>

                          <div class="dropdown-menu {% if link.levels <= 1 %}dropdown-menu--restrictable{% endif %}">
                            <ul class="contents" role="list">
                              {%- for sub_link in link.links -%}
                                <li>
                                  {%- if sub_link.levels == 0 -%}
                                    <a href="{{ sub_link.url }}" class="dropdown-menu__item group" {% if sub_link.current %}aria-current="page"{% endif %}>
                                      <span><span class="reversed-link">{{ sub_link.title }}</span></span>
                                    </a>
                                  {%- else -%}
                                    <details class="relative" is="dropdown-disclosure" trigger="{{ section.settings.menu_open_trigger | escape }}">
                                      <summary class="dropdown-menu__item group" {% if sub_link.url != '#' %}data-url="{{ sub_link.url | escape }}"{% endif %}>
                                        <span><span class="reversed-link">{{- sub_link.title -}}</span></span>
                                        {%- render 'icon' with 'chevron-right' -%}
                                      </summary>

                                      <div class="dropdown-menu">
                                        <ul class="contents" role="list">
                                          {%- for sub_sub_link in sub_link.links -%}
                                            <li>
                                              <a href="{{ sub_sub_link.url }}" class="dropdown-menu__item group" {% if sub_sub_link.current %}aria-current="page"{% endif %}>
                                                <span><span class="reversed-link">{{ sub_sub_link.title }}</span></span>
                                              </a>
                                            </li>
                                          {%- endfor -%}
                                        </ul>
                                      </div>
                                    </details>
                                  {%- endif -%}
                                </li>
                              {%- endfor -%}
                            </ul>
                          </div>
                        </details>
                      {%- endif -%}
                    {%- endif -%}
                  </li>
                {%- endfor -%}
              </ul>
            </nav>
          {%- endif -%}
        </div>
      </div>

      {%- comment -%}
      --------------------------------------------------------------------------------------------------------------------
      LOGO AREA
      --------------------------------------------------------------------------------------------------------------------
      {%- endcomment -%}

      {%- capture logo_content -%}
        {%- if section.settings.logo -%}
          {%- capture sizes -%}{{ section.settings.logo_max_width }}px{%- endcapture -%}
          {%- capture widths -%}{{ section.settings.logo_max_width | times: 2 | at_most: section.settings.logo.width }}, {{ section.settings.logo_max_width | times: 3 | at_most: section.settings.logo.width }}{%- endcapture -%}
          <span class="sr-only">{{ shop.name }}</span>
          {{- section.settings.logo | image_url: width: section.settings.logo.width | image_tag: sizes: sizes, widths: widths, class: 'header__logo-image' -}}

          {%- if section.settings.transparent_logo -%}
            {{- section.settings.transparent_logo | image_url: width: section.settings.transparent_logo.width | image_tag: sizes: sizes, widths: widths, class: 'header__logo-image header__logo-image--transparent' -}}
          {%- endif -%}
        {%- else -%}
          <span class="header__logo-text h4">{{ shop.name }}</span>
        {%- endif -%}
      {%- endcapture -%}

      {%- if request.page_type == 'index' -%}
        <h1 class="header__logo">
          <a href="{{ routes.root_url }}">{{ logo_content }}</a>
        </h1>
      {% else %}
        <a href="{{ routes.root_url }}" class="header__logo">{{ logo_content }}</a>
      {% endif %}

      {%- comment -%}
      --------------------------------------------------------------------------------------------------------------------
      SECONDARY NAV AREA
      --------------------------------------------------------------------------------------------------------------------
      {%- endcomment -%}

      <div class="header__secondary-nav flex">
        {%- if section.settings.header != 'drawer' and section.settings.secondary_menu.links.size > 0 -%}
          <nav class="header__link-list" role="navigation">
            <ul class="contents">
              {%- for link in section.settings.secondary_menu.links -%}
                <li>
                  <a href="{{ link.url }}" class="bold link-faded-reverse">{{ link.title }}</a>
                </li>
              {%- endfor -%}
            </ul>
          </nav>
        {%- endif -%}

        <ul class="header__icon-list mobile-icons">
          {%- if section.settings.show_country_selector and localization.available_countries.size > 1 -%}
            <li class="hidden md:block shrink-0">
              {%- render 'localization-selector', type: 'country', show_country_name: section.settings.show_country_name, show_country_flag: section.settings.show_country_flag, popover_vertical_position: 'end' -%}
            </li>
          {%- endif -%}

          {%- if section.settings.show_locale_selector and localization.available_languages.size > 1 -%}
            <li class="hidden md:block shrink-0">
              {%- render 'localization-selector', type: 'locale', popover_vertical_position: 'end' -%}
            </li>
          {%- endif -%}


          {%- if shop.customer_accounts_enabled -%}
            <li class="hidden sm:block">
              <a href="{% if customer %}{{ routes.account_url }}{% else %}{{ routes.account_login_url }}{% endif %}" class="tap-area">
                <span class="sr-only">{% if customer %}{{ 'header.general.account' | t }}{% else %}{{ 'header.general.login' | t }}{% endif %}</span>
                {%- render 'icon' with 'account' -%}
              </a>
            </li>
          {%- endif -%}

          <li class="mobile-cart">
            {% if cart.item_count == 0 %}
              <a href="{{ settings.cart_empty_button_link }}" class="cart-button {% if section.settings.mobile_cart_style == 'text' %}cart-text{% else %}cart-icon{% endif %}">
                {% if section.settings.mobile_cart_style == 'text' %}
                  BUY NOW
                {% else %}
                  <span class="sr-only">Buy now</span>
                  {%- render 'icon' with 'cart' -%}
                {% endif %}
              </a>
            {% else %}
              <a href="{{ routes.cart_url }}" data-no-instant class="cart-button {% if section.settings.mobile_cart_style == 'text' %}cart-text{% else %}cart-icon{% endif %}" {% if settings.cart_type != 'page' and request.page_type != 'cart' %}aria-controls="cart-drawer"{% endif %}>
                <span class="inline-flex items-center">
                  {% if section.settings.mobile_cart_style == 'text' %}
                    CART(<cart-count>{% if cart.item_count > 0 %}{{ cart.item_count }}{% else %}0{% endif %}</cart-count>)
                  {% else %}
                    <span class="sr-only">{{ 'header.general.cart' | t }}</span>
                    {%- render 'icon' with 'cart' -%}
                    <div class="header__cart-count">
                      <cart-count class="count-bubble {% if cart.item_count == 0 %}opacity-0{% endif %}">
                        <span aria-hidden="true">{{ cart.item_count }}</span>
                      </cart-count>
                    </div>
                  {% endif %}
                </span>
              </a>
            {% endif %}
          </li>

          {% if section.settings.mobile_logo_position == 'left' %}
          <li class="mobile-menu">
            <button type="button" class="mobile-menu-toggle {% if section.settings.layout != 'drawer' %}lg:hidden{% endif %}" aria-controls="header-sidebar-menu" aria-expanded="false" data-action="open-drawer">
              <span class="sr-only">{{ 'header.general.menu' | t }}</span>
              <div class="hamburger-icon">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </button>
          </li>
          {% endif %}
        </ul>
      </div>
    </div>
  </store-header>
</height-observer>
</div>

{%- comment -%}
  --------------------------------------------------------------------------------------------------------------------
  NAVIGATION MENU DRAWER
  --------------------------------------------------------------------------------------------------------------------
{%- endcomment -%}

{%- assign main_menu = section.settings.sidebar_menu | default: section.settings.menu -%}
{%- assign secondary_menu = section.settings.secondary_menu -%}

{%- if main_menu.links.size > 0 or secondary_menu.links.size > 0 -%}
  <navigation-drawer mobile-opening="{{ section.settings.opening_from }}" open-from="left" id="header-sidebar-menu" class="navigation-drawer drawer {% if section.settings.layout != 'drawer' %}lg:hidden{% endif %}" inert {% if request.design_mode %}handle-section-events{% endif %}>
    <button id="mobile-menu-close" is="close-button" aria-label="{{ 'general.accessibility.close' | t | escape }}" {%- if section.settings.opening_from == 'bottom' -%}class="sm-max:hidden"{%- endif -%}>
      {%- render 'icon' with 'close', width: 19, height: 19 -%}
    </button>

    {%- render 'navigation-panel',
      is_mega_menu: false,
      main_menu: main_menu,
      secondary_menu: secondary_menu,
      mobile_opening: section.settings.opening_from,
      layout: section.settings.layout,
      show_social_media: section.settings.show_social_media,
      show_country_selector: section.settings.show_country_selector,
      show_country_name: section.settings.show_country_name,
      show_country_flag: section.settings.show_country_flag,
      show_locale_selector: section.settings.show_locale_selector -%}
  </navigation-drawer>
{%- endif -%}

<script>
  function updateHeader() {
    const headerElem = document.getElementById('shopify-section-{{ section.id }}');
    if (headerElem) {
      const height = Math.round(headerElem.clientHeight) + 'px';
      document.documentElement.style.setProperty('--header-height', height);
      document.documentElement.style.setProperty('--sticky-area-height', height);
    }
  }
  updateHeader();
  window.addEventListener('resize', updateHeader);
  window.addEventListener('load', updateHeader);
  window.addEventListener('orientationchange', updateHeader);
  document.addEventListener('cart:refresh', event => {
    const cartCounts = document.querySelectorAll('cart-count');
    cartCounts.forEach(count => {
      const itemCount = event.detail.cart.item_count || 0;
      count.textContent = itemCount;
    });
  });
</script>

{% schema %}
{
  "name": "Header",
  "class": "shopify-section--header",
  "tag": "header",
  "settings": [
    {
      "type": "checkbox",
      "id": "enable_sticky",
      "label": "Enable sticky header",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "hide_on_scroll",
      "label": "Hide on scroll",
      "default": false
    },
    {
      "type": "header",
      "content": "Logo"
    },
    {
      "type": "image_picker",
      "id": "logo",
      "label": "Image",
      "info": "280 x 80px .png recommended"
    },
    {
      "type": "range",
      "id": "logo_max_width",
      "min": 40,
      "max": 300,
      "step": 5,
      "unit": "px",
      "label": "Image width",
      "default": 140
    },
    {
      "type": "range",
      "id": "logo_mobile_max_width",
      "min": 25,
      "max": 170,
      "step": 5,
      "unit": "px",
      "label": "Mobile image width",
      "default": 100
    },
    {
      "type": "header",
      "content": "Navigation"
    },
    {
      "type": "link_list",
      "id": "menu",
      "label": "Menu",
      "default": "main-menu"
    },
    {
      "type": "link_list",
      "id": "sidebar_menu",
      "label": "Drawer menu"
    },
    {
      "type": "link_list",
      "id": "secondary_menu",
      "label": "Secondary menu",
      "info": "This menu won't show dropdown items. For best results, limit the number of links to 1 or 2."
    },
    {
      "type": "select",
      "id": "menu_open_trigger",
      "label": "Open dropdown items on...",
      "info": "Click mode is forced on touch devices.",
      "options": [
        {
          "value": "hover",
          "label": "Hover"
        },
        {
          "value": "click",
          "label": "Click"
        }
      ],
      "default": "click"
    },
    {
      "type": "header",
      "content": "Mobile navigation"
    },
    {
      "type": "select",
      "id": "mobile_logo_position",
      "label": "Mobile logo position",
      "options": [
        {
          "value": "center",
          "label": "Center (Menu left, Cart right)"
        },
        {
          "value": "left",
          "label": "Left (Cart and Menu right)"
        }
      ],
      "default": "left"
    },
    {
      "type": "select",
      "id": "mobile_cart_style",
      "label": "Mobile cart style",
      "options": [
        {
          "value": "text",
          "label": "Text (CART(0))"
        },
        {
          "value": "icon",
          "label": "Cart icon"
        }
      ],
      "default": "text"
    },
    {
      "type": "color",
      "id": "mobile_cart_background",
      "label": "Cart background color",
      "default": "#2a2a2a"
    },
    {
      "type": "range",
      "id": "mobile_cart_radius",
      "min": 0,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Cart corner radius",
      "default": 10
    },
    {
      "type": "range",
      "id": "cart_text_font_size",
      "min": 8,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "Cart text font size",
      "default": 12
    },
    {
      "type": "range",
      "id": "mobile_cart_text_font_size",
      "min": 8,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "Mobile cart text font size",
      "default": 15
    },
    {
      "type": "select",
      "id": "cart_text_font_weight",
      "label": "Cart text font weight",
      "options": [
        {
          "value": "normal",
          "label": "Normal"
        },
        {
          "value": "bold",
          "label": "Bold"
        }
      ],
      "default": "normal"
    },
    {
      "type": "select",
      "id": "opening_from",
      "label": "Opening from",
      "options": [
        {
          "value": "bottom",
          "label": "Bottom"
        },
        {
          "value": "left",
          "label": "Left"
        }
      ],
      "default": "bottom"
    },
    {
      "type": "header",
      "content": "Desktop"
    },
    {
      "type": "select",
      "id": "layout",
      "label": "Layout",
      "info": "When centering navigation, you should limit your links to 5.",
      "options": [
        {
          "value": "logo_left_navigation_inline",
          "label": "Logo left, navigation inline"
        },
        {
          "value": "logo_left_navigation_center",
          "label": "Logo left, navigation center"
        },
        {
          "value": "logo_center_navigation_inline",
          "label": "Logo center, navigation inline"
        },
        {
          "value": "drawer",
          "label": "Drawer"
        }
      ],
      "default": "logo_left_navigation_inline"
    },
    {
      "type": "header",
      "content": "Social media",
      "info": "This will only show in the drawer navigation."
    },
    {
      "type": "checkbox",
      "id": "show_social_media",
      "label": "Show social media",
      "default": true
    },
    {
      "type": "header",
      "content": "Language selector",
      "info": "To add a language, go to your [language settings.](/admin/settings/languages)"
    },
    {
      "type": "checkbox",
      "id": "show_locale_selector",
      "label": "Show language selector",
      "default": true
    },
    {
      "type": "header",
      "content": "Country/region selector",
      "info": "To add a country/region, go to your [markets settings.](/admin/settings/markets). For best user experience, we recommend to keep this setting off and to use the free [Shopify Geolocation app](https://apps.shopify.com/geolocation)."
    },
    {
      "type": "checkbox",
      "id": "show_country_selector",
      "label": "Show country/region selector",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_country_flag",
      "label": "Show country flag",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_country_name",
      "label": "Show country name",
      "default": false
    },
    {
      "type": "header",
      "content": "Transparent header",
      "info": "Add a section on first position which allows transparent header such as slideshow or image with text overlay."
    },
    {
      "type": "color",
      "id": "transparent_header_text_color",
      "label": "Text color",
      "default": "#ffffff"
    },
    {
      "type": "image_picker",
      "id": "transparent_logo",
      "label": "Logo image",
      "info": "Use the same dimensions as main logo."
    },
    {
      "type": "header",
      "content": "Appearance"
    },
    {
      "type": "checkbox",
      "id": "reduce_padding",
      "label": "Reduce padding",
      "default": false
    },
    {
      "type": "range",
      "id": "background_opacity",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "%",
      "label": "Background opacity",
      "default": 1
    },
    {
      "type": "range",
      "id": "background_blur_radius",
      "min": 0,
      "max": 20,
      "step": 2,
      "unit": "px",
      "label": "Background blur radius",
      "default": 4
    }
  ],
  "blocks": [
    {
      "type": "mega_menu",
      "name": "Mega menu",
      "settings": [
        {
          "type": "text",
          "id": "menu_item",
          "label": "Menu item",
          "info": "Enter menu item to apply a mega menu dropdown. [Learn more](https://support.maestrooo.com/article/180-navigation-enabling-and-configuring-mega-navigation)."
        },
        {
          "type": "select",
          "id": "layout",
          "label": "Layout",
          "options": [
            {
              "value": "horizontal",
              "label": "Horizontal"
            },
            {
              "value": "horizontal_center",
              "label": "Horizontal (centered)"
            },
            {
              "value": "drawer",
              "label": "Drawer"
            }
          ],
          "default": "horizontal"
        },
        {
          "type": "select",
          "id": "submenu_style",
          "label": "Submenu",
          "options": [
            {
              "value": "bold_text",
              "label": "Bold - text only"
            },
            {
              "value": "bold_heading",
              "label": "Bold - headings only"
            }
          ],
          "default": "bold_heading"
        },
        {
          "type": "header",
          "content": "Image 1"
        },
        {
          "type": "image_picker",
          "id": "image_1",
          "label": "Image",
          "info": "1200 x 1200px .jpg recommended"
        },
        {
          "type": "text",
          "id": "image_heading_1",
          "label": "Heading"
        },
        {
          "type": "url",
          "id": "image_link_1",
          "label": "Link"
        },
        {
          "type": "color",
          "id": "image_text_color_1",
          "label": "Heading",
          "default": "#ffffff"
        },
        {
          "type": "header",
          "content": "Image 2"
        },
        {
          "type": "image_picker",
          "id": "image_2",
          "label": "Image",
          "info": "1200 x 1200px .jpg recommended"
        },
        {
          "type": "text",
          "id": "image_heading_2",
          "label": "Heading"
        },
        {
          "type": "url",
          "id": "image_link_2",
          "label": "Link"
        },
        {
          "type": "color",
          "id": "image_text_color_2",
          "label": "Heading",
          "default": "#ffffff"
        },
        {
          "type": "header",
          "content": "Image 3"
        },
        {
          "type": "image_picker",
          "id": "image_3",
          "label": "Image",
          "info": "1200 x 1200px .jpg recommended"
        },
        {
          "type": "text",
          "id": "image_heading_3",
          "label": "Heading"
        },
        {
          "type": "url",
          "id": "image_link_3",
          "label": "Link"
        },
        {
          "type": "color",
          "id": "image_text_color_3",
          "label": "Heading",
          "default": "#ffffff"
        },
        {
          "type": "header",
          "content": "Featured product"
        },
        {
          "type": "product",
          "id": "product",
          "label": "Product"
        },
        {
          "type": "color",
          "id": "product_card_background",
          "label": "Card background"
        },
        {
          "type": "color",
          "id": "product_card_text_color",
          "label": "Card text"
        },
        {
          "type": "header",
          "content": "Appearance"
        },
        {
          "type": "select",
          "id": "promo_content_layout",
          "label": "Promotional content layout",
          "info": "To improve readability, layout can be changed automatically depending on the screen size, menu column count and promotional content. [Learn more](https://support.maestrooo.com/article/296-navigation-understanding-impact-mega-menu-strategy)",
          "options": [
            {
              "value": "grid",
              "label": "Grid"
            },
            {
              "value": "collage",
              "label": "Collage"
            },
            {
              "value": "carousel",
              "label": "Carousel"
            }
          ],
          "default": "grid"
        },
        {
          "type": "checkbox",
          "id": "stretch_promo",
          "label": "Stretch to fill",
          "default": false
        },
        {
          "type": "select",
          "id": "drawer_link_image",
          "label": "Drawer link image",
          "options": [
            {
              "value": "hide",
              "label": "Hide"
            },
            {
              "value": "show",
              "label": "Show product image"
            }
          ],
          "default": "hide"
        }
      ]
    }
  ]
}
{% endschema %}
